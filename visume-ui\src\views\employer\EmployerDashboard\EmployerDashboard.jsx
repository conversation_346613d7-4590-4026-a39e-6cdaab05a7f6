import Cookies from "js-cookie";
import JobDescriptionList from "./JobDescriptionList";
import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { HiOutlineSparkles, HiOutlineBriefcase, HiOutlineTrash, HiOutlinePlus } from "react-icons/hi";
import {
  Header,
  StatsOverview,
  JobList,
  ProfileSkelLoader,
  JobDescriptionModal,
} from "./index";
import StatCard from "../components/StatCard";
import PositionsCard from "../components/PositionsCard";
import ProfileCard from "../ProfilesUI/ProfileCard";
import toast from "react-hot-toast";
import avatar from "assets/img/avatars/avatar4.png";
import { ClipboardCheck, MousePointerClick, Unlock, Video } from "lucide-react";

const EmployerDashboard = () => {
  const jobDescListRef = useRef();
  const emp_id = Cookies.get("employerId");

  const handleJobDescriptionChange = () => {
    if (jobDescListRef.current && jobDescListRef.current.refresh) {
      jobDescListRef.current.refresh();
    }
  };
  const jstoken = Cookies.get("jstoken");
  
  const navigate = useNavigate();
  const [shortListedCandidatesCount, setShortListedCandidatesCount] =
    useState(0);
  const [unlockedCandidatesCount, setUnlockedCandidatesCount] = useState(0);
  const [InterviewedCandidatesCount, setInterviewedCandidatesCount] =
    useState(0);
  const [offeredCandidatesCount, setOfferedCandidatesCount] = useState(0);
  const [empData, setEmpData] = useState({
    name: "Default User1",
    plan_name: "PRO",
    creditsLeft: 100,
  });

  const [isModalOpen, setModalOpen] = useState(false);
  const [jobData, setJobData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  // Job Description state
  const [dashboardJobDescription, setDashboardJobDescription] = useState(null);
  const [jdLoading, setJdLoading] = useState(false);
  const handleShortlist = async (id) => {
    if (loadingId === id) return;
    setLoadingId(id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        toast.error("You need to be an employer to shortlist profiles");
        return;
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/shortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }

      const data = await response.json();
      toast.success(data.message);
      // Remove the shortlisted profile from jobData
      setJobData((prev) =>
        prev.filter((profile) => profile.video_profile_id !== id)
      );
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };
  const [loadingId, setLoadingId] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const filterMatchingProfiles = (profiles, jobDescription) => {
      if (!profiles || !jobDescription) return [];

      return profiles
        .filter((profile) => {
          // Check role match (case-insensitive)
          const roleMatches =
            profile.role.toLowerCase() === jobDescription.role.toLowerCase();

          // Check skills match
          const profileSkills = profile.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const skillsMatch = jdSkills.some((skill) =>
            profileSkills.includes(skill)
          );

          return roleMatches && skillsMatch;
        })
        .sort((a, b) => {
          // Sort by number of matching skills
          const aSkills = a.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const bSkills = b.skills
            .toLowerCase()
            .split(",")
            .map((s) => s.trim());
          const jdSkills = Array.isArray(jobDescription.skills)
            ? jobDescription.skills.map((s) => s.toLowerCase())
            : jobDescription.skills
                .toLowerCase()
                .split(",")
                .map((s) => s.trim());

          const aMatches = jdSkills.filter((skill) =>
            aSkills.includes(skill)
          ).length;
          const bMatches = jdSkills.filter((skill) =>
            bSkills.includes(skill)
          ).length;

          return bMatches - aMatches;
        });
    };

    const getAllProfiles = async () => {
      try {
        setIsLoading(true);

        if (!dashboardJobDescription) {
          setJobData([]);
          setIsLoading(false);
          return;
        }

        const data = await fetch(
          `${
            import.meta.env.VITE_APP_HOST
          }/api/v1/getSuggestedCandidates?emp_id=${emp_id}`
        );
        let res = await data.json();

        if (isMounted && res.candidateProfiles?.length) {
          const matchingProfiles = filterMatchingProfiles(
            res.candidateProfiles,
            dashboardJobDescription
          );
          setJobData(matchingProfiles);
        } else {
          setJobData([]);
        }
        setIsLoading(false);
      } catch (err) {
        console.error(`Error fetching profiles:`, err);
        if (isMounted) {
          setIsLoading(false);
          setJobData([]);
        }
      }
    };

    if (dashboardJobDescription) {
      getAllProfiles();
    }

    return () => {
      isMounted = false;
    };
  }, [emp_id, dashboardJobDescription]);

  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        const profileData = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/employerProfilesData`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: emp_id,
            },
          }
        );
        const profileJson = await profileData.json();

        if (profileJson.data) {
          setShortListedCandidatesCount(
            profileJson?.data?.candidate_counts?.shortlisted_count || 0
          );
          setUnlockedCandidatesCount(
            profileJson?.data?.candidate_counts?.unlocked_count || 0
          );
          setInterviewedCandidatesCount(
            profileJson?.data?.candidate_counts?.interviewed_count || 0
          );
          setOfferedCandidatesCount(
            profileJson?.data?.candidate_counts?.offered_count || 0
          );
          setEmpData({
            name: profileJson?.data?.emp_name || "Default User",
            plan_name: profileJson?.data?.plan_name || "PRO",
            creditsLeft: profileJson?.data?.creditsLeft,
            profile_picture: profileJson?.data?.profile_picture || "",
          });
        }
      } catch (err) {
        console.error("Error fetching shortlisted profiles:", err);
      }
    };

    // Fetch job description for dashboard
    const fetchDashboardJobDescription = async () => {
      setJdLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
        );
        if (response.ok) {
          const data = await response.json();
          setDashboardJobDescription(data.jobDescription || null);
        } else if (response.status === 404) {
          // No job description found, do not log error
          setDashboardJobDescription(null);
        } else {
          // Only log unexpected errors
          console.error(
            "Failed to fetch job description:",
            response.statusText
          );
          setDashboardJobDescription(null);
        }
      } catch (err) {
        console.error("Failed to fetch job description:", err);
        setDashboardJobDescription(null);
      }
      setJdLoading(false);
    };

    fetchCandidates();
    fetchDashboardJobDescription();
  }, [emp_id, isModalOpen]);

  // Delete handler for dashboard JD
  const handleDashboardDeleteJD = async () => {
    if (!dashboardJobDescription?._id) return;
    setJdLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${
          dashboardJobDescription._id
        }`,
        { method: "DELETE" }
      );
      if (response.ok) {
        toast.success("Job description deleted");
        setDashboardJobDescription(null);
      } else {
        toast.error("Failed to delete job description");
      }
    } catch {
      toast.error("Failed to delete job description");
    }
    setJdLoading(false);
  };

  return (
    <>
      {jstoken ? (
        <div className="p-4 space-y-8">
          {/* Streamlined Header with Profile and Stats */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
            <div className="flex items-center justify-between p-6">
              {/* Left: Profile Section */}
              <div className="flex items-center gap-4">
                <img
                  src={
                    empData?.profile_picture
                      ? empData.profile_picture.startsWith("http")
                          ? empData.profile_picture
                          : `${import.meta.env.VITE_APP_HOST}/${empData.profile_picture}`
                      : avatar
                  }
                  alt="User Avatar"
                  className="h-16 w-16 rounded-full shadow-md border-2 border-gray-200 dark:border-gray-700"
                  onError={(e) => {
                    if (e.target.src !== avatar) {
                      e.target.onerror = null;
                      e.target.src = avatar;
                    }
                  }}
                />
                <div className="space-y-1">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {empData?.name || "Loading..."}
                  </h1>
                  <div className="flex items-center gap-3">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 dark:from-blue-900/50 dark:to-purple-900/50 dark:text-blue-200">
                      {empData?.plan_name || "PRO"}
                    </span>
                    <div className="flex items-center gap-1">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Credits:</span>
                      <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                        {empData?.creditsLeft || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Center: Stats */}
              <div className="hidden lg:flex items-center gap-6">
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-3">
                  <div className="p-1.5 bg-white dark:bg-gray-700 rounded-lg">
                    <ClipboardCheck className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white leading-none">
                      {shortListedCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Shortlisted
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-3">
                  <div className="p-1.5 bg-white dark:bg-gray-700 rounded-lg">
                    <Unlock className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white leading-none">
                      {unlockedCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Unlocked
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-3">
                  <div className="p-1.5 bg-white dark:bg-gray-700 rounded-lg">
                    <Video className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white leading-none">
                      {InterviewedCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Interviews
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-3">
                  <div className="p-1.5 bg-white dark:bg-gray-700 rounded-lg">
                    <MousePointerClick className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-xl font-bold text-gray-900 dark:text-white leading-none">
                      {offeredCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Offered
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile Stats */}
            <div className="lg:hidden border-t border-gray-200 dark:border-gray-800 p-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="p-1 bg-white dark:bg-gray-700 rounded">
                    <ClipboardCheck className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900 dark:text-white leading-none">
                      {shortListedCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Shortlisted
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="p-1 bg-white dark:bg-gray-700 rounded">
                    <Unlock className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900 dark:text-white leading-none">
                      {unlockedCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Unlocked
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="p-1 bg-white dark:bg-gray-700 rounded">
                    <Video className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900 dark:text-white leading-none">
                      {InterviewedCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Interviews
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                  <div className="p-1 bg-white dark:bg-gray-700 rounded">
                    <MousePointerClick className="w-3 h-3 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-gray-900 dark:text-white leading-none">
                      {offeredCandidatesCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Offered
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* AI Candidate Matching Section */}
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
              <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-lg">
                    <HiOutlineSparkles className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                      AI Candidate Matching
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {dashboardJobDescription
                        ? `Finding candidates for "${dashboardJobDescription.role}"`
                        : "Upload a job description to discover perfect matches"}
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {jdLoading ? (
                  <div className="flex items-center justify-center py-16">
                    <div className="flex items-center space-x-3">
                      <div className="h-5 w-5 animate-spin rounded-full border-2 border-purple-500 border-t-transparent"></div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Loading job descriptions...
                      </span>
                    </div>
                  </div>
                ) : dashboardJobDescription ? (
                  <div className="space-y-6">
                    {/* Current Job Description Card */}
                    <div className="rounded-lg border border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50 p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                            {dashboardJobDescription.role}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Active job description
                          </p>
                        </div>
                        <button
                          onClick={handleDashboardDeleteJD}
                          className="inline-flex items-center rounded-lg border border-red-300 bg-white px-3 py-2 text-sm font-medium text-red-700 shadow-sm transition-all hover:bg-red-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-700 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-red-900/20"
                        >
                          <HiOutlineTrash className="mr-1.5 h-4 w-4" />
                          Delete
                        </button>
                      </div>
                      <div className="grid gap-4 sm:grid-cols-3">
                        <div className="space-y-1">
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Experience Required
                          </dt>
                          <dd className="text-sm text-gray-900 dark:text-white">
                            {dashboardJobDescription.experience || "Not specified"}
                          </dd>
                        </div>
                        <div className="space-y-1">
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Location
                          </dt>
                          <dd className="text-sm text-gray-900 dark:text-white">
                            {dashboardJobDescription.location || "Not specified"}
                          </dd>
                        </div>
                        <div className="space-y-1">
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Required Skills
                          </dt>
                          <dd className="text-sm text-gray-900 dark:text-white">
                            {Array.isArray(dashboardJobDescription.skills)
                              ? dashboardJobDescription.skills.length
                                ? dashboardJobDescription.skills.join(", ")
                                : "Not specified"
                              : dashboardJobDescription.skills || "Not specified"}
                          </dd>
                        </div>
                      </div>
                    </div>

                    {/* Matching Results */}
                    {isLoading ? (
                      <ProfileSkelLoader />
                    ) : jobData.length > 0 ? (
                      <div className="space-y-3 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="h-2 w-2 rounded-full bg-green-500"></div>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {jobData.length} candidate{jobData.length !== 1 ? 's' : ''} matched
                          </span>
                        </div>
                        {jobData.map((profile, index) => (
                          <ProfileCard
                            key={profile.id || index}
                            {...profile}
                            score={profile.score}
                            onShortlist={() =>
                              handleShortlist(profile.video_profile_id)
                            }
                            isLoading={loadingId === profile.video_profile_id}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="rounded-lg border-2 border-dashed border-gray-300 p-8 text-center dark:border-gray-600">
                        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/20">
                          <HiOutlineBriefcase className="h-6 w-6 text-yellow-600 dark:text-yellow-500" />
                        </div>
                        <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
                          No matching candidates
                        </h3>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                          We couldn't find candidates matching your current requirements.
                          <br />
                          Consider broadening your criteria or check back later.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  /* Empty State - Professional Onboarding */
                  <div className="py-8">
                    <div className="mx-auto max-w-md text-center">
                      <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-2xl bg-gradient-to-r from-purple-500 to-pink-600">
                        <HiOutlineSparkles className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="mt-4 text-lg font-semibold text-gray-900 dark:text-white">
                        Start Finding Great Candidates
                      </h3>
                      <p className="mt-2 text-sm leading-relaxed text-gray-500 dark:text-gray-400">
                        Upload your job description and let our AI match you with
                        qualified Visumes based on skills, experience, and requirements.
                      </p>
                      <button
                        onClick={() => setModalOpen(true)}
                        className="mt-4 inline-flex items-center rounded-lg bg-gradient-to-r from-purple-500 to-pink-600 px-4 py-2 text-sm font-semibold text-white shadow-lg transition-all hover:from-purple-600 hover:to-pink-700 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        <HiOutlineSparkles className="mr-2 h-4 w-4" />
                        Upload Job Description
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Job Descriptions Management */}
            <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-xl shadow-sm">
              <div className="p-6 border-b border-gray-200 dark:border-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 rounded-lg">
                      <HiOutlineBriefcase className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Job Descriptions
                      </h2>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Manage your job postings
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setModalOpen(true)}
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <HiOutlinePlus className="w-4 h-4" />
                    Add New Job
                  </button>
                </div>
              </div>
              <div className="p-6">
                <div className="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800">
                  <JobDescriptionList ref={jobDescListRef} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <JobDescriptionModal
          isOpen={isModalOpen}
          onClose={() => setModalOpen(false)}
        />
      ) : (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Please sign in to continue
            </h2>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              You need to be authenticated to access the dashboard
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default EmployerDashboard;